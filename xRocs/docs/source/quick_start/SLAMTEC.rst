=======================
Start SLAMTEC Chassis
=======================


Steps to Start SLAMTEC Chassis
================================

1. Download the SLAMTEC Chassis SDK from the :download:`SLAMTEC Chassis SDK (Windows) </_static/downloads/RoboStudio_2.1.1_rtm.zip>`.

2. Unzip the downloaded file and run `robostudio.exe` to start the SLAMTEC Chassis SDK (remember to first turn off the Wi-Fi connection).

3. Turn on the Wi-Fi and connect to the SLAMTEC Chassis.

.. image:: /_static/pics/slamtec_chassis/connect.png
  :width: 800px

4. Push the robot around to build the map.

5. Once the map is built (meeting your requirements for navigation), you can use the map editor to erase incorrectly detected obstacles (such as the small black dots shown in the image).

.. image:: /_static/pics/slamtec_chassis/edit.png
  :width: 800px

6. After completing the map editing, you need to upload the map to the chassis hardware (you can also save it locally for future use).

.. image:: /_static/pics/slamtec_chassis/upload.png
  :width: 800px
