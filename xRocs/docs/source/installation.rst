====================
Installation Guide
====================

This guide will help you install the xROCS system.

System Requirements
=====================

xROCS requires the following environment:

* Python 3.10+
* Linux OS (Ubuntu 22.04 or higher recommended)

Installation Steps
====================

Install from whl file
-----------------------

1. Download the whl file

xROCS based on Python 3.9.7 and 3.8.19 has only been tested on AgileX and ARX robots.

.. hlist::
   :columns: 1

   * x86_64 Platform (Python 3.8.19) :download:`xrocs-1.8-cp38-cp38-linux_x86_64.whl </_static/downloads/xrocs-1.8-cp38-cp38-linux_x86_64.whl>`

   * x86_64 Platform (Python 3.9.7) :download:`xrocs-1.8-cp39-cp39-linux_x86_64.whl </_static/downloads/xrocs-1.8-cp39-cp39-linux_x86_64.whl>`

   * x86_64 Platform (Python 3.10.12) :download:`xrocs-1.8-cp310-cp310-linux_x86_64.whl </_static/downloads/xrocs-1.8-cp310-cp310-linux_x86_64.whl>`

   * ARM64 Platform (Python 3.10.12) :download:`xrocs-1.8-cp310-cp310-linux_aarch64.whl </_static/downloads/xrocs-1.8-cp310-cp310-linux_aarch64.whl>`

2. Install the whl file

   .. code-block:: bash

      # x86_64 Platform
      pip install xrocs-1.8-cp3x-cp3x-linux_x86_64.whl

      # ARM64 Platform
      pip install xrocs-1.8-cp310-cp310-linux_aarch64.whl

3. Once the installation is complete, you can verify the installation by running

   .. code-block:: bash

      python3 -c "from xrocs.utils.logger.logger_loader import logger"
      python3 -c "from xrocs.common.data_type import Joints, Pose"

   .. code-block:: python

      from xrocs.utils.logger.logger_loader import logger
      from xrocs.common.data_type import Joints, Pose
      from xrocs.entity.robot.robot_base import RobotArmDriver

      logger.info("Hello World")
      qs = Joints(data=[1., 2., 3., 4., 5., 6.])
      print(qs)

Install TienKung 2.0 and TienYi 2.0 dependencies
--------------------------------------------------

If you want to use TienKung 2.0 or TienYi 2.0, you need to install the following dependencies. This includes the ROS 2 custom messages for motion control.

1. Download the deb file

.. hlist::
   :columns: 1

   * x86_64 Platform :download:`ros-humble-cm-msgs_0.0.1-0jammy_amd64.deb </_static/downloads/ros-humble-cm-msgs_0.0.1-0jammy_amd64.deb>`

   * ARM64 Platform :download:`ros-humble-cm-msgs_0.0.1-0jammy_arm64.deb </_static/downloads/ros-humble-cm-msgs_0.0.1-0jammy_arm64.deb>`

2. Install the deb file

   .. code-block:: bash

      # x86_64 Platform
      sudo dpkg -i ros-humble-cm-msgs_0.0.1-0jammy_amd64.deb

      # ARM64 Platform
      sudo dpkg -i ros-humble-cm-msgs_0.0.1-0jammy_arm64.deb

3. Once the installation is complete, you can verify the installation by running

   .. code-block:: bash

      source /opt/ros/humble/setup.bash
      python3 -c "from cm_msgs.srv import CommonStreamRos2; print('Hello World')"

Install UR dependencies
--------------------------------------------------

If you want to use UR robot, you need to install the ``ur_rtde`` package.

.. code-block:: bash

   pip install ur_rtde==1.6.1

Install Orbbec Camera dependencies
--------------------------------------

If you are also using Orbbec camera, you need to install the ``zerorpc`` package for camera data transmission.

.. code-block:: bash

   pip install zerorpc==0.6.3
