==========================
xROCS Data Type Examples
==========================

This section contains examples of how to use the xROCS data type.

Joints and JointsArray Examples
=================================

.. code-block:: python

    import numpy as np

    from xrocs.common.data_type import BaseData
    from xrocs.common.data_type.joints_data import Joints, JointsArray

Create Data
-------------

.. code-block:: python

    # Single Joints initialization with radian data
    joints = Joints(
        data=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6],
        num_of_dofs=6,
        angle_unit_type=BaseData.AngleUnitType.RADIAN,
    )

    # Single Joints initialization with degree data
    joints = Joints(
        data=[10, 20, 30, 40, 50, 60],
        num_of_dofs=6,
        angle_unit_type=BaseData.AngleUnitType.DEGREE,
    )

    # Single Joints initialization with numpy array
    joints = Joints(
        data=np.array([0.1, 0.2, 0.3, 0.4, 0.5, 0.6]),
        num_of_dofs=6,
        angle_unit_type=BaseData.AngleUnitType.RADIAN,
    )

    # JointsArray initialization with Joints
    joints_array = JointsArray(
        data=[
            Joints(
                data=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6],
                num_of_dofs=6,
                angle_unit_type=BaseData.AngleUnitType.RADIAN,
            ),
            Joints(
                data=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6],
                num_of_dofs=6,
                angle_unit_type=BaseData.AngleUnitType.RADIAN,
            ),
        ],
    )

    # JointsArray initialization with numpy array
    joints_array = JointsArray(
        data=np.array([
            [0.1, 0.2, 0.3, 0.4, 0.5, 0.6],
            [0.1, 0.2, 0.3, 0.4, 0.5, 0.6],
        ]),
        angle_unit_type=BaseData.AngleUnitType.RADIAN,
    )

Operations
------------

.. code-block:: python

    # Single Joints operations
    joints_unit = joints.unit
    joints_radian_ndarray = joints.get_radian_ndarray()
    joints_degree_ndarray = joints.get_degree_ndarray()

    # JointsArray operations
    joints_array_radian_ndarray = joints_array.get_radian_ndarray()
    joints_array_degree_ndarray = joints_array.get_degree_ndarray()

Coordinate and CoordinateArray Examples
=========================================

.. code-block:: python

    import numpy as np

    from xrocs.common.data_type import BaseData
    from xrocs.common.data_type.coordinate_data import Coordinate, CoordinateArray

Create Data
-------------

.. code-block:: python

    # Single Coordinate initialization with meter units
    coordinate = Coordinate(
        data=[1.0, 2.0, 3.0],
        coordinate_system_type=Coordinate.CoordinateSystem.ARM_BASE,
        length_unit_type=BaseData.LengthUnitType.METER,
    )

    # Single Coordinate initialization with millimeter units
    coordinate = Coordinate(
        data=[1000.0, 2000.0, 3000.0],
        coordinate_system_type=Coordinate.CoordinateSystem.ARM_BASE,
        length_unit_type=BaseData.LengthUnitType.MILLIMETER,
    )

    # Single Coordinate initialization with numpy array
    coordinate = Coordinate(
        data=np.array([1.0, 2.0, 3.0]),
        coordinate_system_type=Coordinate.CoordinateSystem.ARM_BASE,
        length_unit_type=BaseData.LengthUnitType.METER,
    )

    # CoordinateArray initialization with Coordinate
    coordinate_array = CoordinateArray(
        array_data=[
            Coordinate(
                data=[1.0, 2.0, 3.0],
                coordinate_system_type=Coordinate.CoordinateSystem.ARM_BASE,
                length_unit_type=BaseData.LengthUnitType.METER,
            ),
            Coordinate(
                data=[1.0, 2.0, 3.0],
                coordinate_system_type=Coordinate.CoordinateSystem.ARM_BASE,
                length_unit_type=BaseData.LengthUnitType.METER,
            ),
        ],
    )

    # CoordinateArray initialization with numpy array
    coordinate_array = CoordinateArray(
        array_data=np.array([
            [1.0, 2.0, 3.0],
            [1.0, 2.0, 3.0],
        ]),
        coordinate_system_type=Coordinate.CoordinateSystem.ARM_BASE,
        length_unit_type=BaseData.LengthUnitType.METER,
    )

Operations
------------

.. code-block:: python

    # Single Coordinate operations
    coordinate_unit = coordinate.unit
    coordinate_xyz_m_ndarray = coordinate.get_xyz_m_ndarray()
    coordinate_xyz_cm_ndarray = coordinate.get_xyz_cm_ndarray()
    coordinate_xyz_mm_ndarray = coordinate.get_xyz_mm_ndarray()

    # CoordinateArray operations
    coordinate_array_xyz_m_ndarray = coordinate_array.get_xyz_m_ndarray()
    coordinate_array_xyz_cm_ndarray = coordinate_array.get_xyz_cm_ndarray()
    coordinate_array_xyz_mm_ndarray = coordinate_array.get_xyz_mm_ndarray()

Rotation Examples
===================

.. code-block:: python

    import math
    import numpy as np

    from xrocs.common.data_type import BaseData
    from xrocs.common.data_type.rotation_data import Rotation

Create Data
-------------

.. code-block:: python

    # Initialization with RPY in radians
    rotation = Rotation(
        data=[math.pi / 4, math.pi / 6, math.pi / 3],
        rotation_form_type=Rotation.FormType.RPY,
        angle_unit_type=BaseData.AngleUnitType.RADIAN,
    )

    # Initialization with RPY in degrees
    rotation = Rotation(
        data=[45.0, 30.0, 60.0],
        rotation_form_type=Rotation.FormType.RPY,
        angle_unit_type=BaseData.AngleUnitType.DEGREE,
    )

    # Initialization with WXYZ
    rotation = Rotation(
        data=[0.7071, 0.0, 0.0, 0.7071],
        rotation_form_type=Rotation.FormType.XYZW,
        angle_unit_type=BaseData.AngleUnitType.RADIAN,
    )

    # Initialization with matrix
    rotation = Rotation(
        data=np.array([[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]]),
        rotation_form_type=Rotation.FormType.MATRIX,
        angle_unit_type=BaseData.AngleUnitType.RADIAN,
    )

Operations
------------

.. code-block:: python

    # Get rotation in different forms
    rotation_rpy_radian = rotation.get_rpy_radian_ndarray()
    rotation_rpy_degree = rotation.get_rpy_degree_ndarray()
    rotation_xyzw = rotation.get_xyzw_ndarray()
    rotation_matrix = rotation.get_matrix_ndarray()

Pose and PoseArray Examples
=============================

.. code-block:: python

    import numpy as np

    from xrocs.common.data_type import BaseData
    from xrocs.common.data_type.coordinate_data import Coordinate
    from xrocs.common.data_type.pose_data import Pose, PoseArray
    from xrocs.common.data_type.rotation_data import Rotation

Create Data
-------------

.. code-block:: python

    # Single pose initialization with Coordinate and Rotation
    pose = Pose(
        coordinate=Coordinate([1.0, 2.0, 3.0], coordinate_system_type=Coordinate.CoordinateSystem.ARM_BASE),
        rotation=Rotation(
            [0.1, 0.2, 0.3], rotation_form_type=Rotation.FormType.RPY, angle_unit_type=BaseData.AngleUnitType.RADIAN
        ),
    )

    # Single pose initialization from affine matrix
    pose = Pose.from_affine_matrix(
        data=np.array([[1.0, 0.0, 0.0, 1.0], [0.0, 1.0, 0.0, 2.0], [0.0, 0.0, 1.0, 3.0], [0.0, 0.0, 0.0, 1.0]]),
        coordinate_system_type=Coordinate.CoordinateSystem.ARM_BASE,
        length_unit_type=BaseData.LengthUnitType.METER,
    )

    # PoseArray initialization with Pose
    pose_array = PoseArray(
        pose_array_data=[
            Pose(
                coordinate=Coordinate([1.0, 2.0, 3.0], coordinate_system_type=Coordinate.CoordinateSystem.ARM_BASE),
                rotation=Rotation(
                    [0.1, 0.2, 0.3], rotation_form_type=Rotation.FormType.RPY, angle_unit_type=BaseData.AngleUnitType.RADIAN
                ),
            ),
            Pose(
                coordinate=Coordinate([1.0, 2.0, 3.0], coordinate_system_type=Coordinate.CoordinateSystem.ARM_BASE),
                rotation=Rotation(
                    [0.1, 0.2, 0.3], rotation_form_type=Rotation.FormType.RPY, angle_unit_type=BaseData.AngleUnitType.RADIAN
                ),
            ),
        ],
    )

Operations
------------

.. code-block:: python

    coordinate = pose.coordinate
    rotation = pose.rotation
    system = pose.system
    x_m = pose.x_m
    y_cm = pose.y_cm
    z_mm = pose.z_mm
    r_radian = pose.r_radian
    p_radian = pose.p_radian
    y_radian = pose.y_radian

    # Get pose in different forms
    pose_xyz_m_rpy_radian = pose.get_xyz_m_rpy_radian_ndarray()
    pose_xyz_mm_rpy_degree = pose.get_xyz_mm_rpy_degree_ndarray()
    pose_affine_matrix_m = pose.get_affine_matrix_m_ndarray()
    pose_xyz_m_xyzw = pose.get_xyz_m_xyzw_ndarray()

    # Translate the pose
    new_pose = pose.translate_x_m(1.0)
    new_pose = pose.translate_y_cm(2.0)
    new_pose = pose.translate_z_cm(3.0)

    # Rotate the pose
    new_pose = pose.rotate_at_self_x_radian(0.1)
    new_pose = pose.rotate_at_self_y_degree(10.0)
    new_pose = pose.rotate_at_self_z_degree(20.0)
