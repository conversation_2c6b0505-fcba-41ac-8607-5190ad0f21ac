=================
xROCS on Franka
=================

This section contains examples of how to use the xROCS API on single Franka and dual Franka standard station.

Examples for Single Franka
============================

Initialize the Single Franka
------------------------------

.. code-block:: python

    from xrocs.common.data_type import Joints
    from xrocs.entity.robot.Franka.franka import <PERSON><PERSON><PERSON><PERSON><PERSON>


    if __name__ == "__main__":
        control_ip = "127.0.0.1"

        # Connect to the franka robot. For Franka single robot station, the arm type does not need to be specified.
        franka = FrankaRobot(robot_ip=control_ip)
        franka.connect()

        # Pre-defined values, this may be different for each robot arm based on their installation method.
        # PLEASE check the values before running the test.
        home_joint = Joints([0.0, -0.7854, 0.0, -1.5708, 0.0, 1.5708, 0], num_of_dofs=7)

        # Add examples here.
        # ...

Common Query Examples
-----------------------

.. code-block:: python

    # Query current joints
    current_joints = franka.get_current_joint()

    # Query current TCP pose
    current_tcp_pose = franka.get_tool_cartesian_pose()

Joints Control Examples
-------------------------

This section shows how to control the robot arm in joint space.

The following code presents how to move the robot arm to a target joint position.

.. code-block:: python

    # Move to pre-defined start joint position
    franka.reach_target_joint(home_joint)


Examples for Dual Franka Standard Station
===========================================

Since all query and control methods are identical for both single Franka and dual Franka standard station, only examples for initialization and common query examples are presented here.

Initialize the Dual Franka Standard Station
---------------------------------------------

.. code-block:: python

    from xrocs.entity.robot.Franka.franka import FrankaRobot
    from xrocs.entity.station.FrankaStd.franka_rs_dual import FrankaDualRS

    if __name__ == "__main__":
        robot_dict = {"left": "127.0.0.1", "right": "127.0.0.1"}
        camera_dict = {}
        hand_dict = {}

        robot_station = FrankaDualRS(robot_dict, camera_dict, hand_dict)
        robot_station.connect()

        robot_handle = robot_station.get_robot_handle()
        camera_handle = robot_station.get_camera_handle()
        hand_handle = robot_station.get_gripper_handle()

        franka_left: FrankaRobot = robot_handle["left"]
        franka_right: FrankaRobot = robot_handle["right"]

        # Add examples here.
        # ...

Common Query Examples
-----------------------

.. code-block:: python

    # Query current joints
    print(f"Current left Franka joints is {franka_left.get_current_joint()}.")
    print(f"Current right Franka joints is {franka_right.get_current_joint()}.")

    # Query current TCP pose
    print(f"Current left Franka TCP pose is {franka_left.get_tool_cartesian_pose()}.")
    print(f"Current right Franka TCP pose is {franka_right.get_tool_cartesian_pose()}.")
