=====================
xROCS on TienYi 2.0
=====================

This section contains examples of how to use the xROCS API on TienYi 2.0 (ROS 2 version).

.. note::

   Remember to (1) connect to the robot before using it (2) clean up resources when the script ends.

A minimal runnable code example is provided at the end of this page.

Initialize the TienYi 2.0 Station
===================================

.. code-block:: python

    import time

    import rclpy

    from xrocs.common.data_type import BaseData, Coordinate, Joints, Pose, PoseArray, Rotation
    from xrocs.entity.robot.XRobot.tienyi_2 import TienYi2Robot, TienYi2RobotXHumanoidSyncUpdateMode
    from xrocs.entity.station.XTY2.x_tienyi_2 import XTY2Station
    from xrocs.utils.logger.logger_loader import logger


    def cleanup_resources() -> None:
        """Cleanup all resources."""
        logger.info("Starting resource cleanup...")

        try:
            # 1. Close ROS 2.
            if rclpy.ok():
                logger.info("Shutting down ROS 2...")
                rclpy.shutdown()

            # 2. Wait for all threads to terminate.
            logger.info("Waiting for threads to terminate...")
            # Get all active threads for the current program except the main thread.
            active_threads = [t for t in threading.enumerate() if t != threading.current_thread()]
            # Wait for non-daemon threads to finish.
            for active_thread in active_threads:
                # Here we only need to close non-daemon threads; daemon threads will be closed automatically.
                if not active_thread.daemon and active_thread.is_alive():
                    logger.info(f"Waiting for thread {active_thread.name} to finish...")
                    active_thread.join(timeout=2.0)
                    if active_thread.is_alive():
                        logger.warning(f"Thread {active_thread.name} did not terminate in time.")

            # 3. Extra waiting time to ensure all resources are released.
            time.sleep(1)

            logger.success("Resource cleanup completed successfully!")

        except Exception as e:
            logger.error(f"Error during resource cleanup: {e}")


    if __name__ == "__main__":
        robot_station, robot_arm = None, None

        try:
            logger.info("Starting XTY2 Station test...")

            robot_dict = {"robot": "127.0.0.1"}
            camera_dict = {}
            hand_dict = {}

            robot_station = XTY2Station(robot_dict, camera_dict, hand_dict)
            robot_arm: TienYi2Robot = robot_station.get_robot_handle()["robot"]
            connect_result = robot_arm.connect()  # IMPORTANT: Connect to robot before using it.

            if not connect_result:
                logger.error("Failed to connect to robot.")
            else:
                robot_arm.disable_moveit_service_mode()
                robot_arm.disable_qp_service_mode()

                logger.info(f"Robot info: {robot_arm}. Number of DOFs: {robot_arm.num_dofs()}.")

                # Add examples here.
                # ...

        except KeyboardInterrupt:
            logger.info("KeyboardInterrupt received in main")

        except Exception as e:
            logger.error(f"Unexpected error in main: {e}")

        finally:
            cleanup_resources()
            logger.info("Script finished.")


MoveIt!-based robot control Examples
======================================

Common Query Examples
-----------------------

The following examples are same as TienKung 2.0.

.. code-block:: python

    # Query current joints
    current_joints = robot_arm.get_current_joint()

    # Query current left TCP
    current_left_tcp = robot_arm.x_humanoid_get_left_tcp_pose_in_waist_yaw_frame()

    # Query current right TCP
    current_right_tcp = robot_arm.x_humanoid_get_right_tcp_pose_in_waist_yaw_frame()

Custom Collision Examples
---------------------------

The following examples are same as TienKung 2.0.

.. code-block:: python

    # Add Obstacles.
    robot_arm.x_humanoid_waypoints_add_custom_obstacles(
        obstacles=[
            {
                "sharp_type": "BOX",
                "object_name": "box1",
                "dimensions": [0.2, 0.2, 0.2],
                "pose": Pose(
                    coordinate=Coordinate(
                        data=[1.0, 0, 1.0],
                        coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                        length_unit_type=BaseData.LengthUnitType.METER,
                    ),
                    rotation=Rotation(data=[0, 0, 0, 1], rotation_form_type=Rotation.FormType.XYZW),
                ),
            },
            {
                "sharp_type": "BOX",
                "object_name": "box2",
                "dimensions": [0.2, 0.2, 0.2],
                "pose": Pose(
                    coordinate=Coordinate(
                        data=[2.0, 0, 2.0],
                        coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                        length_unit_type=BaseData.LengthUnitType.METER,
                    ),
                    rotation=Rotation(data=[0, 0, 0, 1], rotation_form_type=Rotation.FormType.XYZW),
                ),
            },
            {
                "sharp_type": "BOX",
                "object_name": "box3",
                "dimensions": [0.2, 0.2, 0.2],
                "pose": Pose(
                    coordinate=Coordinate(
                        data=[3.0, 0, 3.0],
                        coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                        length_unit_type=BaseData.LengthUnitType.METER,
                    ),
                    rotation=Rotation(data=[0, 0, 0, 1], rotation_form_type=Rotation.FormType.XYZW),
                ),
            },
            {
                "sharp_type": "SPHERE",
                "object_name": "sphere1",
                "dimensions": [0.15],
                "pose": Pose(
                    coordinate=Coordinate(
                        data=[2.0, 0, 1.0],
                        coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                        length_unit_type=BaseData.LengthUnitType.METER,
                    ),
                    rotation=Rotation(data=[0, 0, 0, 1], rotation_form_type=Rotation.FormType.XYZW),
                ),
            },
            {
                "sharp_type": "SPHERE",
                "object_name": "sphere2",
                "dimensions": [0.15],
                "pose": Pose(
                    coordinate=Coordinate(
                        data=[3.0, 0, 1.0],
                        coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                        length_unit_type=BaseData.LengthUnitType.METER,
                    ),
                    rotation=Rotation(data=[0, 0, 0, 1], rotation_form_type=Rotation.FormType.XYZW),
                ),
            },
        ]
    )

    # Remove Some Obstacles.
    robot_arm.x_humanoid_waypoints_remove_custom_obstacles(names=["box1", "sphere1"])

    # Remove All Obstacles.
    robot_arm.x_humanoid_waypoints_remove_all_obstacles()

Cartesian Path Planning Examples
----------------------------------

The following examples are same as TienKung 2.0.

.. code-block:: python

    # Reach Cartesian Pose for both left and right arm (TCP) in sync mode
    robot_arm.x_humanoid_reach_cartesian_pose(
        use_cartesian_path=False,
        left_motion_id=10,
        right_motion_id=10,
        left_waypoints=PoseArray(
            pose_array_data=[
                [0.319, 0.24303, 0.064, 0.030847, -0.70643, -0.030894, 0.70644],
                [0.3514, 0.24357, 0.17982, 0.030847, -0.70643, -0.030894, 0.70644],
            ],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
        right_waypoints=PoseArray(
            pose_array_data=[
                [0.319, -0.24303, 0.064, -0.030847, -0.70643, 0.030894, 0.70644],
                [0.3514, -0.24357, 0.17982, -0.030847, -0.70643, 0.030894, 0.70644],
            ],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

    # Reach Cartesian Pose only for left arm (TCP)
    robot_arm.x_humanoid_reach_cartesian_pose(
        use_cartesian_path=True,
        left_motion_id=11,
        left_waypoints=PoseArray(
            pose_array_data=[
                [0.05, 0, 0, 0, 0, 0, 1],
                [0.05, 0, 0.05, 0, 0, 0, 1],
                [0.05, 0, -0.1, 0, 0, 0, 1],
                [0.0, 0.1, 0, 0, 0, 0, 1],
                [0.0, -0.1, 0, 0, 0, 0, 1],
            ],
            coordinate_system_type=Coordinate.CoordinateSystem.LEFT_TCP_LINK,
            length_unit_type=BaseData.LengthUnitType.METER,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

    # Reach Cartesian Pose for both left and right arm (TCP) in async mode
    robot_arm.x_humanoid_reach_cartesian_pose(
        use_cartesian_path=True,
        left_motion_id=12,
        left_waypoints=PoseArray(
            pose_array_data=[[0.05, 0, 0, 0, 0, 0, 1], [0.05, 0, -0.05, 0, 0, 0, 1]],
            coordinate_system_type=Coordinate.CoordinateSystem.LEFT_TCP_LINK,
            length_unit_type=BaseData.LengthUnitType.METER,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    time.sleep(1.0)
    robot_arm.x_humanoid_reach_cartesian_pose(
        use_cartesian_path=True,
        right_motion_id=13,
        right_waypoints=PoseArray(
            pose_array_data=[[0.05, 0, 0, 0, 0, 0, 1], [0.05, 0, -0.05, 0, 0, 0, 1]],
            coordinate_system_type=Coordinate.CoordinateSystem.RIGHT_TCP_LINK,
            length_unit_type=BaseData.LengthUnitType.METER,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

Joint Space Planning Examples
-------------------------------

The following examples are same as TienKung 2.0.

.. code-block:: python

    # Reach target joints for only left arm
    robot_arm.x_humanoid_reach_joints(
        motion_mode="left_arm",
        left_target_joints=Joints(
            data=[-0.52333, 0.52333, 0.52333, -0.52333, -0.52333, -0.52333, -0.52333],
            num_of_dofs=7,
            angle_unit_type=BaseData.AngleUnitType.RADIAN,
        ),
        right_target_joints=None,
    )
    # Waiting for Joints Control Action to Finish...
    robot_arm.x_humanoid_wait_joints_control_action_finish()

    # Reach target joints for only right arm
    robot_arm.x_humanoid_reach_joints(
        motion_mode="right_arm",
        left_target_joints=None,
        right_target_joints=Joints(
            data=[-0.52333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], num_of_dofs=7, angle_unit_type=BaseData.AngleUnitType.RADIAN
        ),
    )
    # Waiting for Joints Control Action to Finish...
    robot_arm.x_humanoid_wait_joints_control_action_finish()

     # Reach target joints for both left and right arm in sync mode
    robot_arm.x_humanoid_reach_joints(
        motion_mode="dual_arm",
        left_target_joints=Joints(
            data=[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], num_of_dofs=7, angle_unit_type=BaseData.AngleUnitType.RADIAN
        ),
        right_target_joints=Joints(
            data=[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], num_of_dofs=7, angle_unit_type=BaseData.AngleUnitType.RADIAN
        ),
    )
    # Waiting for Joints Control Action to Finish...
    robot_arm.x_humanoid_wait_joints_control_action_finish()

Surrounding Obstacle Perception and Planning Examples
-------------------------------------------------------

.. code-block:: python

    # Enable and Disable Surrounding Obstacle Perception to check the effect
    robot_arm.x_humanoid_enable_surrounding_obstacle_perception()
    robot_arm.x_humanoid_disable_surrounding_obstacle_perception()

    # Enable Surrounding Obstacle Perception
    robot_arm.x_humanoid_enable_surrounding_obstacle_perception()
    # Reach Cartesian Pose for both left and right arm (TCP) in sync mode
    robot_arm.x_humanoid_reach_cartesian_pose(
        use_cartesian_path=True,
        left_motion_id=10,
        right_motion_id=10,
        left_waypoints=PoseArray(
            pose_array_data=[
                [0.319, 0.24303, 0.064, 0.030847, -0.70643, -0.030894, 0.70644],
                [0.3514, 0.24357, 0.17982, 0.030847, -0.70643, -0.030894, 0.70644],
            ],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
        right_waypoints=PoseArray(
            pose_array_data=[
                [0.319, -0.24303, 0.064, -0.030847, -0.70643, 0.030894, 0.70644],
                [0.3514, -0.24357, 0.17982, -0.030847, -0.70643, 0.030894, 0.70644],
            ],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

    # Disable Surrounding Obstacle Perception
    robot_arm.x_humanoid_disable_surrounding_obstacle_perception()
    # Reach Cartesian Pose for both left and right arm (TCP) in sync mode
    robot_arm.x_humanoid_reach_cartesian_pose(
        use_cartesian_path=True,
        left_motion_id=11,
        right_motion_id=11,
        left_waypoints=PoseArray(
            pose_array_data=[
                [0.319, 0.24303, 0.064, 0.030847, -0.70643, -0.030894, 0.70644],
                [0.3514, 0.24357, 0.17982, 0.030847, -0.70643, -0.030894, 0.70644],
            ],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
        right_waypoints=PoseArray(
            pose_array_data=[
                [0.319, -0.24303, 0.064, -0.030847, -0.70643, 0.030894, 0.70644],
                [0.3514, -0.24357, 0.17982, -0.030847, -0.70643, 0.030894, 0.70644],
            ],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

QP-based robot control Examples
=================================

Cartesian Path Planning Examples
----------------------------------

.. code-block:: python

    # Reach cartesian pose only for left arm (TCP)
    robot_arm.x_humanoid_sync_reach_cartesian_pose(
        left_waypoints=PoseArray(
            pose_array_data=[[0.55, 0.25, 0.8, 0.306, -0.7188, 0.0781, 0.61911]],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
        right_waypoints=None,
        print_details=True,
    )
    # Waiting for waypoints action to finish...
    robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop(timeout=25.0)

    # Update mode
    robot_arm.x_humanoid_sync_update_mode(TienYi2RobotXHumanoidSyncUpdateMode.WAIST_ONLY)
    # Set time limit
    robot_arm.x_humanoid_sync_set_time_limit(1.0)
    # Set speed limit
    robot_arm.x_humanoid_sync_set_speed_limit(1.5)

    # Reach cartesian pose only for left arm (TCP)
    robot_arm.x_humanoid_sync_reach_cartesian_pose(
        left_waypoints=PoseArray(
            pose_array_data=[[0.55, 0.5, 1.2, 0.306, -0.7188, 0.0781, 0.61911]],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
        right_waypoints=None,
    )
    # Waiting for waypoints action to finish...
    robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop()

    # Set time limit to default
    robot_arm.x_humanoid_sync_set_time_limit()

    # Reach cartesian pose only for left arm (TCP)
    robot_arm.x_humanoid_sync_reach_cartesian_pose(
        left_waypoints=PoseArray(
            pose_array_data=[[0.55, 0.5, 1.2, 0.306, -0.7188, 0.0781, 0.61911]],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
        right_waypoints=None,
    )
    # Waiting for waypoints action to finish...
    robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop()

    # Update mode
    robot_arm.x_humanoid_sync_update_mode(TienYi2RobotXHumanoidSyncUpdateMode.BOTH_MOVE)

    # Reach cartesian pose step-by-step
    robot_arm.x_humanoid_sync_reach_cartesian_pose(
        left_waypoints=PoseArray(
            pose_array_data=[[0.55, 0.25, 0.8, 0.306, -0.7188, 0.0781, 0.61911]],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
        right_waypoints=None,
    )
    robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop()
    robot_arm.x_humanoid_sync_reach_cartesian_pose(
        left_waypoints=PoseArray(
            pose_array_data=[[0.55, 0.5, 1.2, 0.306, -0.7188, 0.0781, 0.61911]],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
        right_waypoints=None,
    )

    # Reach Cartesian Pose for both left and right arm (TCP) in sync mode
    robot_arm.x_humanoid_sync_reach_cartesian_pose(
        left_waypoints=PoseArray(
            pose_array_data=[[0.55, 0.5, 1.0, 0.306, -0.7188, 0.0781, 0.61911]],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
        right_waypoints=PoseArray(
            pose_array_data=[[0.45, -0.45, 1.0, -0.3188, -0.7108, -0.06291, 0.623711]],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop()

    # Send cartesian pose for only left arm at high frequency
    for i in range(100):
        robot_arm.x_humanoid_sync_reach_cartesian_pose(
            left_waypoints=PoseArray(
                pose_array_data=[[0.55, 0.5, 1.0 - 0.002 * i, 0.306, -0.7188, 0.0781, 0.61911]],
                coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                rotation_form_type=Rotation.FormType.XYZW,
            ),
            right_waypoints=None,
        )

        time.sleep(0.5)  #  You can try different frequency

Joint Space Planning Examples
-------------------------------

.. code-block:: python

    # Query current joints
    current_joints = robot_arm.get_current_joint()

    # Move to new joint position
    target_joints = Joints(
        data=[
            0.0,
            0.0,
            0.0,
            -2.39684e-05,
            -0.71924,
            0.56023,
            0.28251,
            -0.88366,
            -0.0048418,
            0.00565958,
            -0.00740957,
            -0.717399,
            -0.571234,
            -0.250062,
            -0.883717,
            0.00491333,
            -0.00222635,
            -0.00840377,
        ],
        num_of_dofs=robot_arm.num_dofs(),
        angle_unit_type=BaseData.AngleUnitType.RADIAN,
    )
    target_joints_radian_ndarray = target_joints.get_radian_ndarray()
    target_joints_radian_ndarray[3] -= 0.6
    new_target = Joints(
        data=target_joints_radian_ndarray,
        num_of_dofs=robot_arm.num_dofs(),
        angle_unit_type=BaseData.AngleUnitType.RADIAN,
    )
    robot_arm.x_humanoid_sync_joints(target_joint=new_target, print_details=True)

Minimal Runnable Code Example
===============================

.. code-block:: python

    def switch_examples():
        input("----- 1. Send Left Joints. -----")
        robot_arm.x_humanoid_reach_joints(
            motion_mode="left_arm",
            left_target_joints=Joints(
                data=[-0.52333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
                num_of_dofs=7,
                angle_unit_type=BaseData.AngleUnitType.RADIAN,
            ),
            right_target_joints=None,
        )
        print("----- Waiting for Joints Control Action to Finish... -----")
        robot_arm.x_humanoid_wait_joints_control_action_finish()

        input("----- 2. Update QP mode. -----")
        robot_arm.x_humanoid_sync_update_mode(TienYi2RobotXHumanoidSyncUpdateMode.LEGS_ONLY)

        input("----- 3. Set time limit. -----")
        robot_arm.x_humanoid_sync_set_time_limit(3.0)

        input("----- 4. Reach Cartesian pose Left TCP. -----")
        robot_arm.x_humanoid_sync_reach_cartesian_pose(
            left_waypoints=PoseArray(
                pose_array_data=[[0.6468, 0.2792, 0.75232, -4.8541e-06, -0.76595, 3.8485e-06, 0.6429]],
                coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                rotation_form_type=Rotation.FormType.XYZW,
            ),
            right_waypoints=None,
        )
        print("----- Waiting for Waypoints Action to Finish... -----")
        robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop()

    def cleanup_resources() -> None:
        """Cleanup all resources."""
        logger.info("Starting resource cleanup...")

        try:
            # 1. Close ROS 2.
            if rclpy.ok():
                logger.info("Shutting down ROS 2...")
                rclpy.shutdown()

            # 2. Wait for all threads to terminate.
            logger.info("Waiting for threads to terminate...")
            # Get all active threads for the current program except the main thread.
            active_threads = [t for t in threading.enumerate() if t != threading.current_thread()]
            # Wait for non-daemon threads to finish.
            for active_thread in active_threads:
                # Here we only need to close non-daemon threads; daemon threads will be closed automatically.
                if not active_thread.daemon and active_thread.is_alive():
                    logger.info(f"Waiting for thread {active_thread.name} to finish...")
                    active_thread.join(timeout=2.0)
                    if active_thread.is_alive():
                        logger.warning(f"Thread {active_thread.name} did not terminate in time.")

            # 3. Extra waiting time to ensure all resources are released.
            time.sleep(1)

            logger.success("Resource cleanup completed successfully!")

        except Exception as e:
            logger.error(f"Error during resource cleanup: {e}")


    if __name__ == "__main__":
        robot_station, robot_arm = None, None

        try:
            logger.info("Starting XTY2 Station test...")

            robot_dict = {"robot": "127.0.0.1"}
            camera_dict = {}
            hand_dict = {}

            robot_station = XTY2Station(robot_dict, camera_dict, hand_dict)
            robot_arm: TienYi2Robot = robot_station.get_robot_handle()["robot"]
            connect_result = robot_arm.connect()  # IMPORTANT: Connect to robot before using it.

            if not connect_result:
                logger.error("Failed to connect to robot.")
            else:
                robot_arm.disable_moveit_service_mode()
                robot_arm.disable_qp_service_mode()

                logger.info(f"Robot info: {robot_arm}. Number of DOFs: {robot_arm.num_dofs()}.")

                switch_examples()

        except KeyboardInterrupt:
            logger.info("KeyboardInterrupt received in main")

        except Exception as e:
            logger.error(f"Unexpected error in main: {e}")

        finally:
            cleanup_resources()
            logger.info("Script finished.")
