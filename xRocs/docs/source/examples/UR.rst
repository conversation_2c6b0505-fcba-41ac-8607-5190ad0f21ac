=============
xROCS on UR
=============

This section contains examples of how to use the xROCS API on single UR and dual UR standard station.

Examples for Single UR
========================

Initialize the Single UR
--------------------------

.. code-block:: python

    import time

    import numpy as np

    from xrocs.common.data_type import BaseData, Coordinate, Joints, Pose, Rotation
    from xrocs.entity.robot.UR.ur import URRobot


    if __name__ == "__main__":
        robot_ip = "*************"  # UR Left Arm.
        ur = URRobot(robot_ip)
        ur.connect()

        # Pre-defined values, this may be different for each robot arm based on their installation method.
        # PLEASE check the values before running the test.
        start_joints_value = [-1.52264, -1.98747, -2.56776, -0.15027, -2.35548, -0.03371]
        target_joints_value = [-1.52264, -1.98747, -2.56776, -0.15027, -2.35548, -0.03371 - 0.5]
        start_joints = Joints(data=start_joints_value, num_of_dofs=6, angle_unit_type=BaseData.AngleUnitType.RADIAN)
        target_joints = Joints(data=target_joints_value, num_of_dofs=6, angle_unit_type=BaseData.AngleUnitType.RADIAN)

        start_pose_value = [-0.04656, -0.33578, 0.23483, -0.03758, 0.78208, 0.01079]
        target_pose_value = [-0.04656 + 0.1, -0.33578, 0.23483, -0.03758, 0.78208, 0.01079]
        start_pose = Pose(
            Coordinate(start_pose_value[:3]), Rotation(start_pose_value[3:], rotation_form_type=Rotation.FormType.RPY)
        )
        target_pose = Pose(
            Coordinate(target_pose_value[:3]), Rotation(target_pose_value[3:], rotation_form_type=Rotation.FormType.RPY)
        )

        # Add examples here.
        # ...

Common Query Examples
-----------------------

.. code-block:: python

    # Query current joints
    current_joints = ur.get_current_joint()

    # Query current TCP pose
    current_tcp_pose = ur.get_tool_cartesian_pose()

Joints Control Examples
-------------------------

This section shows how to control the robot arm in joint space.

The following code presents how to move the robot arm to a target joint position.

.. code-block:: python

    # Move to pre-defined start joint position
    ur.reach_target_joint(target_joint=start_joints, asynchronous=False)

    # Query current joints
    current_joints = ur.get_current_joint()

    # Move to new joint position
    target_joints_radian_ndarray = current_joints.get_radian_ndarray()
    target_joints_radian_ndarray[5] -= 0.5
    target_joints = Joints(target_joints_radian_ndarray)
    ur.reach_target_joint(target_joint=target_joints, asynchronous=False)

The following code presents a comparison of different joint reach methods.

Direct Move Mode
^^^^^^^^^^^^^^^^^^

.. code-block:: python

    # Move to pre-defined start joint position
    ur.reach_target_joint(target_joint=start_joints, asynchronous=False)
    # Move to pre-defined target joint position
    ur.reach_target_joint(target_joint=target_joints, asynchronous=False)

Manual Delay Mode
^^^^^^^^^^^^^^^^^^^

.. warning::
    This mode may cause the robotic arm to start and stop frequently, which can lead to severe wear. Use with caution.

.. code-block:: python

    # Move to pre-defined start joint position
    ur.reach_target_joint(target_joint=start_joints, asynchronous=False)
    # Move to pre-defined target joint position step-by-step
    joints_tarjectory = np.linspace(start_joints_value, target_joints_value, 100)
    for joints in joints_tarjectory:
        ur.reach_target_joint(
            target_joint=Joints(data=joints, num_of_dofs=6, angle_unit_type=BaseData.AngleUnitType.RADIAN),
            asynchronous=False,
        )
        time.sleep(0.1)

Servo Control Mode
^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

    # Move to pre-defined start joint position
    ur.reach_target_joint(target_joint=start_joints, asynchronous=False)
    # Move to pre-defined target joint position step-by-step in servo mode
    for joints in joints_tarjectory:
        ur.sync_target_joint(target_joint=joints)

Cartesian Pose Control Examples
---------------------------------

This section shows how to control the robot arm in Cartesian space.

The following code presents how to move the robot arm to a target Cartesian pose.

.. code-block:: python

    # Move to pre-defined start joint position
    ur.reach_target_joint(target_joint=start_joints, asynchronous=False)

    # Query current TCP pose
    current_tcp_pose = ur.get_tool_cartesian_pose()

    # Move to new TCP pose
    target_tcp_pose_xyz_m_rpy_rad = current_tcp_pose.get_xyz_m_rpy_radian_ndarray()
    target_tcp_pose_xyz_m_rpy_rad[0] += 0.1
    ur.reach_tool_cartesian_pose(
        pose=Pose(
            Coordinate(target_tcp_pose_xyz_m_rpy_rad[:3]),
            Rotation(target_tcp_pose_xyz_m_rpy_rad[3:], rotation_form_type=Rotation.FormType.RPY),
        )
    )

The following code presents a comparison of different Cartesian pose reach methods.

Direct Move Mode
^^^^^^^^^^^^^^^^^^

.. code-block:: python

    # Move to pre-defined start joint position
    ur.reach_target_joint(target_joint=start_joints, asynchronous=False)
    # Move to pre-defined target TCP pose
    ur.reach_tool_cartesian_pose(target_pose)

Manual Delay Mode
^^^^^^^^^^^^^^^^^^^

.. warning::
    This mode may cause the robotic arm to start and stop frequently, which can lead to severe wear. Use with caution.

.. code-block:: python

    # Move to pre-defined start joint position
    ur.reach_target_joint(target_joint=start_joints, asynchronous=False)
    # Move to pre-defined target TCP pose step-by-step
    pose_tarjectory = np.linspace(start_pose_value, target_pose_value, 100)
    for pose in pose_tarjectory:
        ur.reach_tool_cartesian_pose(
            pose=Pose(Coordinate(pose[:3]), Rotation(pose[3:], rotation_form_type=Rotation.FormType.RPY))
        )
        time.sleep(0.1)

Servo Control Mode
^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

    # Move to pre-defined start joint position
    ur.reach_target_joint(target_joint=start_joints, asynchronous=False)
    # Move to pre-defined target TCP pose step-by-step in servo mode
    for pose in pose_tarjectory:
        ur.sync_tool_cartesian_pose(pose)


Examples for Dual UR Standard Station
=======================================

Since all query and control methods are identical for both single UR and dual UR standard station, only examples for initialization and common query examples are presented here.

Initialize the Dual UR Standard Station
-----------------------------------------

.. code-block:: python

    from xrocs.entity.robot.UR.ur import URRobot
    from xrocs.entity.station.URStd.ur_std_station import URStdStation

    if __name__ == "__main__":
        robot_dict = {"robot_left": "*************", "robot_right": "*************"}
        camera_dict = {}
        hand_dict = {}

        robot_station = URStdStation(robot_dict, camera_dict, hand_dict)
        robot_station.connect()

        robot_handle = robot_station.get_robot_handle()
        camera_handle = robot_station.get_camera_handle()
        hand_handle = robot_station.get_gripper_handle()

        ur_left: URRobot = robot_handle["robot_left"]
        ur_right: URRobot = robot_handle["robot_right"]

        # Add examples here.
        # ...

Common Query Examples
-----------------------

.. code-block:: python

    # Query current joints
    print(f"Current left UR joints is {ur_left.get_current_joint()}.")
    print(f"Current right UR joints is {ur_right.get_current_joint()}.")

    # Query current TCP pose
    print(f"Current left UR TCP pose is {ur_left.get_tool_cartesian_pose()}.")
    print(f"Current right UR TCP pose is {ur_right.get_tool_cartesian_pose()}.")
