==========================
xROCS on Robotiq Gripper
==========================

This section contains examples of how to use the xROCS API on Robotiq gripper.

Examples for Robotiq Gripper (USB Client)
===========================================

.. warning::

    Considering port conflict, the USB-based robotiq gripper control requires each gripper to be connected to the robot via a separated USB port instead of sharing the same port.

Initialize the Gripper Client
-------------------------------

.. code-block:: python

    import time

    import numpy as np

    from xrocs.entity.hand.RobotiqUSB.robotiq_client import Joints, RobotiqGripperClient


    if __name__ == "__main__":
        left_hand = RobotiqGripperClient(hand_ip="************", hand_port="4242")
        right_hand = RobotiqGripperClient(hand_ip="************", hand_port="4243")
        left_hand.connect()
        right_hand.connect()

        # Add examples here.
        # ...

Gripper Open and Close Examples
---------------------------------

.. code-block:: python

    # Open left hand
    left_hand.open()

    # Close left hand
    left_hand.close()

In addition, you can also perform customized gripper control by manipulating the gripper joint position, either by directly specifying custom joint position or by sending target positions at a high frequency.

.. code-block:: python

    # Directly specify custom joint position
    left_hand.set_target_joint(Joints(np.asarray([0.8]), num_of_dofs=1))

.. code-block:: python

    # Send target positions at a high frequency
    freq = 400
    for value in range(freq):
        left_hand.sync_target_joint(target_joint=np.asarray([value * 1.0 / freq]))
        time.sleep(1.0 / freq)
