==========================
xROCS on SLAMTEC Chassis
==========================

This section contains examples of how to control the SLAMTEC chassis using the xROCS API.

.. note::

    Things to know before starting:

    1. The IP of SLAMTEC chassis is ************

    2. The Web SDK controls can be accessed at http://************:1448

A minimal runnable code example is provided at the end of this page.

Initialize the SLAMTEC Chassis
================================

.. code-block:: python

    import math

    from xrocs.entity.robot.XRobot.slamtec_chassis import NavigationApiService
    from xrocs.utils.logger.logger_loader import logger


    if __name__ == "__main__":
        base_url = "************"
        nav_service = NavigationApiService(config={'navigation_api_url': f'http://{base_url}:1448'}, chassis_data_service=None)

        # Add examples here.
        # ...

Navigation Examples
=====================

SLAMTEC chassis supports sending a navigate point or rotate to yaw action request each time to control the movement of the robot chassis. It should be noted that

1. When sending navigate point action request, the yaw cannot take effect; the rotation operation needs to be performed separately.

2. The chassis is capable of automatically avoiding obstacles during navigation.

The following examples will show how to send navigate point and rotate to yaw action requests.

.. code-block:: python

    result = nav_service.perform_navigate_point(navigate_point={"x": 0.84, "y": 2.07, "yaw": math.radians(90)})

    result = nav_service.perform_rotate_to_yaw(rotate_to_yaw={"yaw": math.radians(90)})

RC Joystick Examples
======================

The following examples will show how to send remote control (RC) joystick action requests. All actions start from the current pose of the chassis.

.. code-block:: python

    # Move forward for 2 seconds.
    nav_service.send_move_by_action_request({"direction": 0, "duration": 2000})

    # Move backward for 2 seconds.
    nav_service.send_move_by_action_request({"direction": 1, "duration": 2000})

    # Move right for 2 seconds.
    nav_service.send_move_by_action_request({"direction": 2, "duration": 2000})

    # Move left for 3 seconds.
    nav_service.send_move_by_action_request({"direction": 3, "duration": 3000})

Minimal Runnable Code Example
===============================

.. code-block:: python

    import math

    from xrocs.entity.robot.XRobot.slamtec_chassis import NavigationApiService
    from xrocs.utils.logger.logger_loader import logger

    def nav_examples() -> None:
        """Navigation examples."""
        input("----- 1. Navigate to point 1 (i.e., the given x, y, and yaw), the yaw is invalid. -----")
        result = nav_service.perform_navigate_point(navigate_point={"x": 0.84, "y": 2.07, "yaw": math.radians(90)})
        logger.info(f"Navigate result: {result}")

        input("----- 2. Rotate to yaw (90 degrees). -----")
        result = nav_service.perform_rotate_to_yaw(rotate_to_yaw={"yaw": math.radians(90)})
        logger.info(f"Rotate to yaw result: {result}")

        input("----- 3. Navigate to point 1 (i.e., the given x, y, and yaw), the yaw is invalid. -----")
        result = nav_service.perform_navigate_point(navigate_point={"x": 1.72, "y": 1.97, "yaw": math.radians(90)})
        logger.info(f"Navigate result: {result}")

        input("----- 4. Rotate to yaw (180 degrees). -----")
        result = nav_service.perform_rotate_to_yaw(rotate_to_yaw={"yaw": math.radians(180)})
        logger.info(f"Rotate to yaw result: {result}")

    def moveby_examples():
        input("----- 1. Move backward 2 seconds. -----")
        nav_service.send_move_by_action_request({"direction": 1, "duration": 2000})

        input("----- 2. Move forward 2 seconds. -----")
        nav_service.send_move_by_action_request({"direction": 0, "duration": 2000})

        input("----- 3. Move right 2 seconds. -----")
        nav_service.send_move_by_action_request({"direction": 2, "duration": 2000})

        input("----- 4. Move left 3 seconds. -----")
        nav_service.send_move_by_action_request({"direction": 3, "duration": 3000})


    if __name__ == "__main__":
        base_url = "************"
        nav_service = NavigationApiService(config={'navigation_api_url': f'http://{base_url}:1448'}, chassis_data_service=None)

        nav_examples()
        moveby_examples()
