=========
Examples
=========

This section contains examples of how to use the xROCS API.

.. toctree::
   :maxdepth: 1
   :caption: xROCS Data Type Examples
   :name: data_type_examples
   :class: data_type_examples

   data_type

.. toctree::
   :maxdepth: 1
   :caption: xROCS on TienYi 2.0
   :name: tienyi_2_examples
   :class: tienyi_2_examples

   TienYi_2.0

.. toctree::
   :maxdepth: 1
   :caption: xROCS on TienKung 2.0
   :name: tienkung_2_examples
   :class: tienkung_2_examples

   TienKung_2.0

.. toctree::
   :maxdepth: 1
   :caption: xROCS on UR
   :name: ur_examples
   :class: ur_examples

   UR

.. toctree::
   :maxdepth: 1
   :caption: xROCS on Franka
   :name: franka_examples
   :class: franka_examples

   Franka

.. toctree::
   :maxdepth: 1
   :caption: xROCS on Robotiq
   :name: robotiq_examples
   :class: robotiq_examples

   Robotiq

.. toctree::
   :maxdepth: 1
   :caption: xROCS on SLAMTEC Chassis
   :name: slamtec_examples
   :class: slamtec_examples

   SLAMTEC
