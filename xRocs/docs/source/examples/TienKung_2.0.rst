=======================
xROCS on TienKung 2.0
=======================

This section contains examples of how to use the xROCS API on TienKung 2.0.

Initialize the TienKung 2.0 Station
=====================================

.. code-block:: python

    import time

    import rclpy

    from xrocs.common.data_type import BaseData, Coordinate, Joints, Pose, PoseArray, Rotation
    from xrocs.entity.robot.XRobot.tienkung_2 import TienKung2Robot
    from xrocs.entity.station.XTK2.x_tienkung_2 import XTK2Station
    from xrocs.utils.logger.logger_loader import logger


    if __name__ == "__main__":
        try:
            robot_dict = {"robot": "127.0.0.1"}
            camera_dict = {}
            hand_dict = {}

            robot = XTK2Station(robot_dict, camera_dict, hand_dict)
            robot_arm: TienKung2Robot = robot.get_robot_handle()["robot"]
            # robot_arm.connect()

            # Add examples here.
            # ...

        except Exception as e:
            logger.error(f"Error: {e}")

        finally:
            # Make sure to clean up resources when the script ends.
            if "robot" in locals():
                # Wait for all actions to finish.
                robot_arm.x_humanoid_wait_waypoints_action_finish()
                # Set to safe state.
                robot_arm.disable_moveit_service_mode()
                robot_arm.disable_qp_service_mode()
                # Wait for threads and processors to finish.
                time.sleep(1)
                # Close ROS 2.
                rclpy.shutdown()
                # Give threads time to terminate correctly.
                time.sleep(1)
            logger.info("Script finished.")

MoveIt!-based robot control Examples
======================================

Common Query Examples
-----------------------

The following examples are same as TienYi 2.0.

.. code-block:: python

    # Query current joints
    current_joints = robot_arm.get_current_joint()

    # Query current left TCP
    current_left_tcp = robot_arm.x_humanoid_get_left_tcp_pose_in_waist_yaw_frame()

    # Query current right TCP
    current_right_tcp = robot_arm.x_humanoid_get_right_tcp_pose_in_waist_yaw_frame()

Custom Collision Examples
---------------------------

The following examples are same as TienYi 2.0.

.. code-block:: python

    # Add Obstacles.
    robot_arm.x_humanoid_waypoints_add_custom_obstacles(
        obstacles=[
            {
                "sharp_type": "BOX",
                "object_name": "box1",
                "dimensions": [0.2, 0.2, 0.2],
                "pose": Pose(
                    coordinate=Coordinate(
                        data=[1.0, 0, 1.0],
                        coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                        length_unit_type=BaseData.LengthUnitType.METER,
                    ),
                    rotation=Rotation(data=[0, 0, 0, 1], rotation_form_type=Rotation.FormType.XYZW),
                ),
            },
            {
                "sharp_type": "BOX",
                "object_name": "box2",
                "dimensions": [0.2, 0.2, 0.2],
                "pose": Pose(
                    coordinate=Coordinate(
                        data=[2.0, 0, 2.0],
                        coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                        length_unit_type=BaseData.LengthUnitType.METER,
                    ),
                    rotation=Rotation(data=[0, 0, 0, 1], rotation_form_type=Rotation.FormType.XYZW),
                ),
            },
            {
                "sharp_type": "BOX",
                "object_name": "box3",
                "dimensions": [0.2, 0.2, 0.2],
                "pose": Pose(
                    coordinate=Coordinate(
                        data=[3.0, 0, 3.0],
                        coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                        length_unit_type=BaseData.LengthUnitType.METER,
                    ),
                    rotation=Rotation(data=[0, 0, 0, 1], rotation_form_type=Rotation.FormType.XYZW),
                ),
            },
            {
                "sharp_type": "SPHERE",
                "object_name": "sphere1",
                "dimensions": [0.15],
                "pose": Pose(
                    coordinate=Coordinate(
                        data=[2.0, 0, 1.0],
                        coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                        length_unit_type=BaseData.LengthUnitType.METER,
                    ),
                    rotation=Rotation(data=[0, 0, 0, 1], rotation_form_type=Rotation.FormType.XYZW),
                ),
            },
            {
                "sharp_type": "SPHERE",
                "object_name": "sphere2",
                "dimensions": [0.15],
                "pose": Pose(
                    coordinate=Coordinate(
                        data=[3.0, 0, 1.0],
                        coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
                        length_unit_type=BaseData.LengthUnitType.METER,
                    ),
                    rotation=Rotation(data=[0, 0, 0, 1], rotation_form_type=Rotation.FormType.XYZW),
                ),
            },
        ]
    )

    # Remove Some Obstacles.
    robot_arm.x_humanoid_waypoints_remove_custom_obstacles(names=["box1", "sphere1"])

    # Remove All Obstacles.
    robot_arm.x_humanoid_waypoints_remove_all_obstacles()

Cartesian Path Planning Examples
----------------------------------

The following examples are same as TienYi 2.0.

.. code-block:: python

    # Reach Cartesian Pose for both left and right arm (TCP) in sync mode
    robot_arm.x_humanoid_reach_cartesian_pose(
        use_cartesian_path=False,
        left_motion_id=10,
        right_motion_id=10,
        left_waypoints=PoseArray(
            pose_array_data=[
                [0.319, 0.24303, 0.064, 0.030847, -0.70643, -0.030894, 0.70644],
                [0.3514, 0.24357, 0.17982, 0.030847, -0.70643, -0.030894, 0.70644],
            ],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
        right_waypoints=PoseArray(
            pose_array_data=[
                [0.319, -0.24303, 0.064, -0.030847, -0.70643, 0.030894, 0.70644],
                [0.3514, -0.24357, 0.17982, -0.030847, -0.70643, 0.030894, 0.70644],
            ],
            coordinate_system_type=Coordinate.CoordinateSystem.WAIST_YAW_LINK,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

    # Reach Cartesian Pose only for left arm (TCP)
    robot_arm.x_humanoid_reach_cartesian_pose(
        use_cartesian_path=True,
        left_motion_id=11,
        left_waypoints=PoseArray(
            pose_array_data=[
                [0.05, 0, 0, 0, 0, 0, 1],
                [0.05, 0, 0.05, 0, 0, 0, 1],
                [0.05, 0, -0.1, 0, 0, 0, 1],
                [0.0, 0.1, 0, 0, 0, 0, 1],
                [0.0, -0.1, 0, 0, 0, 0, 1],
            ],
            coordinate_system_type=Coordinate.CoordinateSystem.LEFT_TCP_LINK,
            length_unit_type=BaseData.LengthUnitType.METER,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

    # Reach Cartesian Pose for both left and right arm (TCP) in async mode
    robot_arm.x_humanoid_reach_cartesian_pose(
        use_cartesian_path=True,
        left_motion_id=12,
        left_waypoints=PoseArray(
            pose_array_data=[[0.05, 0, 0, 0, 0, 0, 1], [0.05, 0, -0.05, 0, 0, 0, 1]],
            coordinate_system_type=Coordinate.CoordinateSystem.LEFT_TCP_LINK,
            length_unit_type=BaseData.LengthUnitType.METER,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    time.sleep(1.0)
    robot_arm.x_humanoid_reach_cartesian_pose(
        use_cartesian_path=True,
        right_motion_id=13,
        right_waypoints=PoseArray(
            pose_array_data=[[0.05, 0, 0, 0, 0, 0, 1], [0.05, 0, -0.05, 0, 0, 0, 1]],
            coordinate_system_type=Coordinate.CoordinateSystem.RIGHT_TCP_LINK,
            length_unit_type=BaseData.LengthUnitType.METER,
            rotation_form_type=Rotation.FormType.XYZW,
        ),
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

Joint Space Planning Examples
-------------------------------

The following examples are same as TienYi 2.0.

.. code-block:: python

    # Reach target joints for only left arm
    robot_arm.x_humanoid_reach_joints(
        motion_mode="left_arm",
        left_target_joints=Joints(
            data=[-0.52333, 0.52333, 0.52333, -0.52333, -0.52333, -0.52333, -0.52333],
            num_of_dofs=7,
            angle_unit_type=BaseData.AngleUnitType.RADIAN,
        ),
        right_target_joints=None,
    )
    # Waiting for Joints Control Action to Finish...
    robot_arm.x_humanoid_wait_joints_control_action_finish()

    # Reach target joints for only right arm
    robot_arm.x_humanoid_reach_joints(
        motion_mode="right_arm",
        left_target_joints=None,
        right_target_joints=Joints(
            data=[-0.52333, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], num_of_dofs=7, angle_unit_type=BaseData.AngleUnitType.RADIAN
        ),
    )
    # Waiting for Joints Control Action to Finish...
    robot_arm.x_humanoid_wait_joints_control_action_finish()

     # Reach target joints for both left and right arm in sync mode
    robot_arm.x_humanoid_reach_joints(
        motion_mode="dual_arm",
        left_target_joints=Joints(
            data=[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], num_of_dofs=7, angle_unit_type=BaseData.AngleUnitType.RADIAN
        ),
        right_target_joints=Joints(
            data=[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], num_of_dofs=7, angle_unit_type=BaseData.AngleUnitType.RADIAN
        ),
    )
    # Waiting for Joints Control Action to Finish...
    robot_arm.x_humanoid_wait_joints_control_action_finish()

Surrounding Obstacle Perception and Planning Examples
-------------------------------------------------------

Comming soon...

QP-based robot control Examples
=================================

Cartesian Path Planning Examples
----------------------------------

Comming soon...

Joint Space Planning Examples
-------------------------------

Comming soon...
