/* Custom CSS for xROCS documentation */

/* Global typography improvements */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Enhanced header styling */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #1a73e8;
    margin-top: 1.5em;
    margin-bottom: 0.8em;
    letter-spacing: -0.01em;
}

h1 {
    font-size: 2.2em;
    border-bottom: 2px solid #e8f0fe;
    padding-bottom: 0.5em;
    margin-bottom: 1em;
}

h2 {
    font-size: 1.8em;
    border-bottom: 1px solid #e8f0fe;
    padding-bottom: 0.3em;
    margin-top: 2em;
}

h3 {
    font-size: 1.4em;
    color: #3c4043;
}

h4 {
    font-size: 1.2em;
    color: #3c4043;
}

/* Modern code block styling */
pre {
    background-color: #f8f9fa;
    border: 1px solid #eaecef;
    border-radius: 6px;
    padding: 16px;
    font-size: 14px;
    line-height: 1.6;
    overflow-x: auto;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    margin: 1.5em 0;
}

code {
    background-color: rgba(175, 184, 193, 0.2);
    border-radius: 4px;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    color: #24292f;
}

.highlight {
    margin: 1em 0 1.5em 0;
}

.highlight pre {
    margin: 0;
}

/* Enhanced table styling */
table.docutils {
    width: 100%;
    margin: 1.5em 0;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.07);
}

table.docutils th {
    background-color: #f1f3f4;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    border: none;
    border-bottom: 2px solid #e1e4e5;
    color: #3c4043;
}

table.docutils td {
    padding: 12px 15px;
    border: none;
    border-bottom: 1px solid #e1e4e5;
}

table.docutils tr:last-child td {
    border-bottom: none;
}

table.docutils tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Improved sidebar navigation */
.wy-nav-side {
    background-color: #f2f4f8;
    border-right: 1px solid #e4e8ee;
}

.wy-side-scroll {
    background-color: #f2f4f8;
}

.wy-side-nav-search {
    background: linear-gradient(135deg, #1a73e8, #4285f4);
    margin-bottom: 0;
    padding: 1em;
}

.wy-menu-vertical a {
    padding: 0.6em 1.2em;
    color: #424b5a;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    background-color: transparent;
    font-size: 0.95em;
    margin: 1px 0;
}

.wy-menu-vertical a:hover {
    background-color: #e8f0fe;
    border-left: 3px solid #1a73e8;
    color: #1a73e8;
}

/* 一级目录标题样式 - 更加醒目 */
.wy-menu-vertical p.caption {
    color: #ffffff;
    font-weight: 600;
    margin-top: 1.5em;
    padding: 0;
    text-transform: uppercase;
    font-size: 0.9em;
    letter-spacing: 0.5px;
    margin-bottom: 0.5em;
    background-color: #1a73e8;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    position: relative;
    display: flex;
    align-items: center;
    height: 2.5em;
}

/* 添加小图标和文字垂直居中 */
.wy-menu-vertical p.caption::before {
    content: '';
    display: inline-block;
    width: 5px;
    height: 100%;
    background-color: #4285f4;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 4px 0 0 4px;
}

.wy-menu-vertical p.caption span {
    padding-left: 15px;
    display: flex;
    align-items: center;
    height: 100%;
}

/* Enhanced section anchor links */
a.headerlink {
    visibility: hidden;
    margin-left: 0.4em;
    color: #1a73e8;
    opacity: 0.6;
    font-size: 0.8em;
    transition: opacity 0.2s ease;
}

h1:hover a.headerlink,
h2:hover a.headerlink,
h3:hover a.headerlink,
h4:hover a.headerlink,
h5:hover a.headerlink,
h6:hover a.headerlink {
    visibility: visible;
    opacity: 1;
}

a.headerlink:hover {
    text-decoration: none;
    opacity: 1;
}

/* Modern admonition (callout) boxes */
.admonition {
    border-radius: 8px;
    margin: 1.5em 0;
    padding: 0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-left: 4px solid transparent;
}

.admonition-title {
    margin: 0;
    padding: 12px 15px;
    font-weight: 600;
    font-size: 1em;
    display: flex;
    align-items: center;
}

.admonition-title::before {
    margin-right: 8px;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
}

.admonition .admonition-title + p {
    margin-top: 0;
}

.admonition p {
    padding: 0 15px 12px 15px;
    margin: 0;
}

.admonition p:last-child {
    margin-bottom: 0;
}

/* Note admonition */
.admonition.note {
    background-color: #f6fafd;
    border-left-color: #1a73e8;
}
.admonition.note .admonition-title {
    background-color: #e8f0fe;
    color: #1a73e8;
}
.admonition.note .admonition-title::before {
    content: "\f05a"; /* Font Awesome info icon */
}

/* Warning admonition */
.admonition.warning {
    background-color: #fff8e6;
    border-left-color: #f9ab00;
}
.admonition.warning .admonition-title {
    background-color: #fef7e0;
    color: #e37400;
}
.admonition.warning .admonition-title::before {
    content: "\f071"; /* Font Awesome warning icon */
}

/* Danger/Error admonition */
.admonition.danger {
    background-color: #fef7f6;
    border-left-color: #d93025;
}
.admonition.danger .admonition-title {
    background-color: #fdeded;
    color: #d93025;
}
.admonition.danger .admonition-title::before {
    content: "\f06a"; /* Font Awesome exclamation icon */
}

/* Tip admonition */
.admonition.tip {
    background-color: #f2fbf6;
    border-left-color: #34a853;
}
.admonition.tip .admonition-title {
    background-color: #e6f4ea;
    color: #34a853;
}
.admonition.tip .admonition-title::before {
    content: "\f0eb"; /* Font Awesome lightbulb icon */
}

/* Enhanced API documentation styling */
dl.class, dl.function, dl.method, dl.attribute, dl.module {
    margin-bottom: 2.5em;
    padding: 20px;
    border-left: 4px solid #1a73e8;
    background-color: #f8f9fa;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
}

dl.class::before, dl.function::before, dl.method::before, dl.attribute::before, dl.module::before {
    content: attr(class);
    position: absolute;
    top: -10px;
    right: 15px;
    background: #e8f0fe;
    color: #1a73e8;
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

dl.class::before { content: "Class"; }
dl.function::before { content: "Function"; }
dl.method::before { content: "Method"; }
dl.attribute::before { content: "Attribute"; }
dl.module::before { content: "Module"; }

dt {
    font-weight: 600;
    margin-bottom: 0.8em;
    font-size: 1.1em;
}

dt.sig {
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    background-color: #f1f3f4;
    padding: 10px;
    border-radius: 6px;
    margin: 0 -10px 1em -10px;
    line-height: 1.5;
    overflow-x: auto;
    white-space: pre-wrap;
}

/* Improved sidebar active state */
.wy-menu-vertical li.current {
    background-color: #e8edf5;
}

.wy-menu-vertical li.current > a {
    color: #1a73e8;
    font-weight: 600;
    border-top: none;
    border-bottom: none;
    border-left: 3px solid #1a73e8;
    background-color: #dae6f5;
}

.wy-menu-vertical li.current > a:hover {
    background-color: #d2e3fc;
}

.wy-menu-vertical li.toctree-l2.current > a,
.wy-menu-vertical li.toctree-l3.current > a,
.wy-menu-vertical li.toctree-l4.current > a {
    background-color: #e8f0fe;
}

.wy-menu-vertical li.toctree-l2.current,
.wy-menu-vertical li.toctree-l3.current,
.wy-menu-vertical li.toctree-l4.current {
    background-color: #eef1f7;
}

.wy-menu-vertical li.toctree-l2 a,
.wy-menu-vertical li.toctree-l3 a,
.wy-menu-vertical li.toctree-l4 a {
    color: #4b5366;
    padding-left: 1.8em;
    font-size: 0.9em;
    border-left: 2px solid transparent;
}

.wy-menu-vertical li.toctree-l2 a:hover,
.wy-menu-vertical li.toctree-l3 a:hover,
.wy-menu-vertical li.toctree-l4 a:hover {
    color: #1a73e8;
}

/* Modern search box styling */
.wy-side-nav-search {
    padding: 1.2em;
    background: linear-gradient(135deg, #1a73e8, #4285f4);
    margin-bottom: 0;
}

.wy-side-nav-search > a {
    color: white;
    font-weight: 500;
    font-size: 1.2em;
    margin-bottom: 0.6em;
}

.wy-side-nav-search input[type="text"] {
    border-radius: 50px;
    padding: 12px 18px;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.wy-side-nav-search input[type="text"]:focus {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Modern navigation button styling */
.btn {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-neutral {
    background-color: white !important;
    color: #1a73e8 !important;
    border-color: #e8f0fe !important;
}

.btn-neutral:hover {
    background-color: #e8f0fe !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.rst-content .prev-next-bottom a.left-prev,
.rst-content .prev-next-bottom a.right-next {
    min-width: 120px;
}

.rst-content .prev-next-bottom a.left-prev::before {
    content: "← ";
}

.rst-content .prev-next-bottom a.right-next::after {
    content: " →";
}

/* Enhanced API documentation specific styling */
.module-name {
    font-weight: 600;
    color: #1a73e8;
    font-size: 1.1em;
    padding: 3px 6px;
    background-color: #e8f0fe;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 0.5em;
}

.class-name {
    font-weight: 600;
    color: #3c4043;
    font-size: 1.05em;
}

.viewcode-link {
    color: #1a73e8;
    font-size: 0.8em;
    float: right;
    background-color: #e8f0fe;
    padding: 2px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.viewcode-link:hover {
    background-color: #d2e3fc;
    text-decoration: none;
}

/* Inheritance diagram improvements */
div.graphviz {
    overflow-x: auto;
    margin: 1em 0;
}

/* Improved parameter list styling */
dl.field-list {
    display: grid;
    grid-template-columns: minmax(150px, auto) 1fr;
    grid-gap: 10px 15px;
    margin: 1.5em 0;
    border-radius: 6px;
    overflow: hidden;
    background-color: #f8f9fa;
    padding: 10px;
}

dl.field-list > dt {
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: 600;
    color: #3c4043;
    background-color: #f1f3f4;
    display: flex;
    align-items: center;
    grid-column: 1;
}

dl.field-list > dd {
    padding: 8px 0;
    margin-left: 0;
    grid-column: 2;
}

dl.field-list p {
    margin: 0;
}

/* Return values and exceptions styling */
dl.field-list > dt.field-odd:nth-of-type(2n+1) {
    background-color: #f8f8f8;
}

/* RTD theme overrides */
.rst-content code.literal {
    color: #d23669;
    background-color: #f8f9fa;
    border: 1px solid #e1e4e8;
    font-weight: 500;
}

/* Enhanced class inheritance display */
.inherited-list {
    margin: 1em 0;
    padding: 10px 15px;
    background-color: #f1f3f4;
    border-radius: 6px;
    border-left: 3px solid #1a73e8;
}

.inherited-list .first {
    font-weight: 500;
    color: #3c4043;
    margin-bottom: 0.5em;
}

/* Additional global styling for better readability */
.rst-content {
    max-width: 950px;
    margin: 0 auto;
    padding: 1.5em;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .rst-content {
        padding: 1em;
    }

    h1 {
        font-size: 1.8em;
    }

    h2 {
        font-size: 1.5em;
    }

    dl.field-list {
        grid-template-columns: 1fr;
    }

    dl.field-list > dt,
    dl.field-list > dd {
        grid-column: 1;
    }
}

/* Add subtle hover effect to content links */
.rst-content a {
    color: #1a73e8;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s ease;
}

.rst-content a:hover {
    border-bottom: 1px solid #1a73e8;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}
