/* xROCS 首页特定样式 */

/* 英雄横幅区域 */
.hero-banner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    margin: -2rem -2rem 2rem -2rem;
    background: linear-gradient(135deg, #1a73e8, #8ab4f8);
    color: white;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.hero-logo {
    margin-bottom: 1.5rem;
}

.hero-logo img {
    height: 120px;
    width: auto;
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin: 0;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border: none;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin: 1rem 0 2rem 0;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-button {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
}

.hero-button.primary {
    background-color: white;
    color: #1a73e8;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
}

.hero-button.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.hero-button.secondary {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    border: 2px solid white;
}

.hero-button.secondary:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

/* 特性卡片网格 */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.feature-card {
    background-color: white;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-top: 4px solid #1a73e8;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
}

.feature-icon {
    display: block;
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem auto;
    background-color: #e8f0fe;
    border-radius: 50%;
    position: relative;
}

.feature-icon::before {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    color: #1a73e8;
}

.core-icon::before { content: "\f085"; } /* 齿轮 */
.common-icon::before { content: "\f0ad"; } /* 扳手 */
.entity-icon::before { content: "\f1b2"; } /* 立方体 */
.utils-icon::before { content: "\f7d9"; } /* 工具箱 */
.apps-icon::before { content: "\f5fc"; } /* 机器人 */
.simulation-icon::before { content: "\f24d"; } /* 克隆 */

.feature-card h3 {
    font-size: 1.4rem;
    margin: 0 0 1rem 0;
    color: #1a73e8;
}

.feature-card p {
    margin: 0;
    color: #555;
    line-height: 1.5;
}

/* 文档部分样式 */
.doc-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.toctree-wrapper {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.toctree-wrapper:hover {
    transform: translateY(-3px);
}

.toctree-wrapper .caption {
    color: #1a73e8;
    font-weight: 600;
    font-size: 1.2rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e8f0fe;
    margin-bottom: 1rem;
}

.toctree-wrapper ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
}

.toctree-wrapper li {
    margin-bottom: 0.5rem;
}

.toctree-wrapper a {
    display: block;
    padding: 0.5rem;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
}

.toctree-wrapper a:hover {
    background-color: #f5f9ff;
    color: #1a73e8;
    padding-left: 0.8rem;
}

/* 页脚横幅 */
.footer-banner {
    background: linear-gradient(135deg, #34a853, #1e8e3e);
    border-radius: 10px;
    padding: 3rem 2rem;
    margin: 4rem 0 2rem 0;
    text-align: center;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.footer-content h2 {
    font-size: 2rem;
    margin: 0 0 1rem 0;
    color: white;
    border: none;
}

.footer-content p {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto 2rem auto;
    opacity: 0.9;
}

.footer-button {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    background-color: white;
    color: #34a853;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.footer-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

/* 响应式调整 */
@media (min-width: 768px) {
    .hero-banner {
        flex-direction: row;
        text-align: left;
        padding: 5rem 3rem;
    }

    .hero-logo {
        margin-right: 2rem;
        margin-bottom: 0;
    }

    .hero-content {
        max-width: 600px;
    }

    .hero-buttons {
        justify-content: flex-start;
    }
}

@media (max-width: 767px) {
    .hero-content h1 {
        font-size: 2.2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .feature-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer-content h2 {
        font-size: 1.8rem;
    }
}
