{% if not obj.display %}
:orphan:

{% endif %}
{% block title %}

{{ obj.name }} 类
{{ "=" * (obj.name|length + 7) }}

{% endblock %}

.. py:currentmodule:: {{ obj.module }}

.. py:class:: {{ obj.name }}{% if obj.args %}({{ obj.args }}){% endif %}
   {% if obj.bases %}
   {% if obj.bases|length == 1 %}

   **继承自**: :class:`{{ obj.bases[0] }}`
   {% else %}

   **继承自**:
      {% for base in obj.bases %}
      * :class:`{{ base }}`
      {% endfor %}
   {% endif %}

   {% endif %}

{% block body %}
{% if obj.docstring %}
.. autoapi-nested-parse::

   {{ obj.docstring|prepare_docstring|indent(3) }}

{% endif %}

{% block inheritance %}
{% if obj.inheritance %}
{% set first = true %}
{% for class in obj.inheritance %}
{% if first %}

*继承关系图*

.. inheritance-diagram:: {{ class.name }}
   :parts: 1

{% set first = false %}
{% endif %}
{% endfor %}
{% endif %}
{% endblock %}

{% block methods_summary %}
{% if obj.methods %}

方法概述
--------------

{% for method in obj.methods|sort(attribute="name") %}
* :meth:`{{ method.name }}` - {{ method.short_description }}
{% endfor %}

{% endif %}
{% endblock %}

{% block attributes_summary %}
{% if obj.attributes %}

属性概述
-----------------

{% for attribute in obj.attributes|sort(attribute="name") %}
* :attr:`{{ attribute.name }}` - {{ attribute.short_description }}
{% endfor %}

{% endif %}
{% endblock %}

{% block methods %}
{% if obj.methods %}

方法
-------

{% for method in obj.methods|sort(attribute="name") %}
.. py:method:: {{ method.name }}{% if method.args %}({{ method.args }}){% endif %}
   {% if method.docstring %}

   {{ method.docstring|prepare_docstring|indent(3) }}
   {% endif %}

{% endfor %}

{% endif %}
{% endblock %}

{% block attributes %}
{% if obj.attributes %}

属性
----------

{% for attribute in obj.attributes|sort(attribute="name") %}
.. py:attribute:: {{ attribute.name }}

   {% if attribute.docstring %}
   {{ attribute.docstring|prepare_docstring|indent(3) }}
   {% endif %}

{% endfor %}

{% endif %}
{% endblock %}

{% endblock %}
