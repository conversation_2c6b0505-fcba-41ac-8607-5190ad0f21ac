# ACT  debug save images
# python\ 
# 分布式训练
# torchrun --standalone --nproc_per_node=4 \   

export HF_HUB_OFFLINE=1
export TRANSFORMERS_OFFLINE=1

conda activate lerobot
torchrun --standalone --nproc_per_node=4 \ 
    -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/stock_cloth_2 \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.enable_save_transformed=true \
    --dataset.image_transforms.save_transformed_dir=custom_debug_images \
    --dataset.image_transforms.save_transformed_steps="[0]" \
    --dataset.image_transforms.save_original_images=false \
    --dataset.episodes="[$(seq -s, 0 999)]" \
    --output_dir=outputs/train/act_stock_cloth_2_training_chunk30_warmup5k_cos_$(date +%Y%m%d_%H%M%S) \
    --job_name=stock_cloth_2_training_chunk30_warmup5000_cos \
    --wandb.project=stock_cloth_2_training_chunk30_warmup5000_cos \
    --policy.n_action_steps=30 \
    --policy.chunk_size=30 \
    --policy.kl_weight=5.0 \
    --policy.optimizer_lr=5e-5 \
    --policy.dropout=0.05 \
    --batch_size=20 \
    --steps=800000 \
    --use_policy_training_preset=false \
    --optimizer.type=adamw \
    --optimizer.lr=5e-5 \
    --optimizer.weight_decay=0.0001 \
    --scheduler.type=cosine_decay_with_warmup \
    --scheduler.num_warmup_steps=10000 \
    --scheduler.num_decay_steps=800000 \
    --scheduler.peak_lr=5e-5 \
    --scheduler.decay_lr=1e-6 \
    --save_freq=5000 \
    --log_freq=200 \
    --policy.type=act \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --dataset.video_backend=pyav \
    --eval_freq=0 \
    --policy.device=cuda \
    --wandb.disable_artifact=true \
    --wandb.enable=true \
    --wandb.mode=offline

# ACT 4090 training 
conda activate lerobot
python3 \
    -m lerobot.scripts.train \
    --dataset.repo_id=pick_up \
    --dataset.root=/home/<USER>/data/tmp/tiangong/lerobot \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.enable_save_transformed=true \
    --dataset.image_transforms.save_transformed_dir=custom_debug_images \
    --dataset.image_transforms.save_transformed_steps="[0]" \
    --dataset.image_transforms.save_original_images=false \
    --output_dir=outputs/train/act_pickup_chunk50_batch100_w5k_cos_$(date +%Y%m%d_%H%M%S) \
    --job_name=act_pickup_chunk50_batch100_w5k_cos \
    --wandb.project=act_pickup_chunk50_batch100_w5k_cos \
    --policy.n_action_steps=50 \
    --policy.chunk_size=50 \
    --policy.kl_weight=5.0 \
    --policy.optimizer_lr=9e-5 \
    --policy.dropout=0.05 \
    --batch_size=100 \
    --use_policy_training_preset=false \
    --optimizer.type=adamw \
    --optimizer.lr=9e-5 \
    --optimizer.weight_decay=0.0001 \
    --scheduler.type=cosine_decay_with_warmup \
    --scheduler.num_warmup_steps=10000 \
    --scheduler.num_decay_steps=500000 \
    --scheduler.peak_lr=9e-5 \
    --scheduler.decay_lr=1.5e-6 \
    --steps=150000 \
    --save_freq=5000 \
    --log_freq=100 \
    --policy.type=act \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --eval_freq=0 \
    --policy.device=cuda \
    --wandb.disable_artifact=true \
    --wandb.enable=true \
    --wandb.mode=offline


# ACT resnet50 
conda activate lerobot;
python -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/pick_up_parts_less/lerobot \
    --policy.type=act \
    --policy.vision_backbone=resnet50 \
    --policy.pretrained_backbone_weights=ResNet50_Weights.IMAGENET1K_V1 \
    --policy.optimizer_lr_backbone=1e-6 \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --output_dir=outputs/train/pick_up_parts_less_act_resnet50_$(date +%Y%m%d_%H%M%S) \
    --job_name=pick_up_parts_less_resnet50_training \
    --policy.device=cuda \
    --steps=300000 \
    --save_freq=10000 \
    --wandb.disable_artifact=true\
    --log_freq=500\
    --batch_size=16 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.project=pick_up_parts_training \
    --policy.n_action_steps=10 \
    --policy.chunk_size=10 \
    --policy.kl_weight=1.0 \
    --policy.optimizer_lr=5e-5 \
    --policy.dropout=0.05 \
    --eval_freq=0

# ACT resnet50 with cosine learning rate scheduler - 优化版本  loss 后期更低
conda activate lerobot;
python -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/pick_up_parts_less/lerobot \
    --policy.type=act \
    --policy.vision_backbone=resnet50 \
    --policy.pretrained_backbone_weights=ResNet50_Weights.IMAGENET1K_V1 \
    --policy.optimizer_lr=3e-5 \
    --policy.optimizer_lr_backbone=5e-7 \
    --policy.kl_weight=0.5 \
    --policy.dropout=0.1 \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --output_dir=outputs/train/pick_up_parts_less_act_resnet50_cos_$(date +%Y%m%d_%H%M%S) \
    --job_name=pick_up_parts_less_resnet50_cos_training \
    --policy.device=cuda \
    --steps=300000 \
    --save_freq=10000 \
    --wandb.disable_artifact=true \
    --log_freq=500 \
    --batch_size=16 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.project=pick_up_parts_training \
    --policy.n_action_steps=10 \
    --policy.chunk_size=10 \
    --use_policy_training_preset=false \
    --optimizer.type=adamw \
    --optimizer.lr=3e-5 \
    --optimizer.weight_decay=1e-4 \
    --optimizer.grad_clip_norm=10.0 \
    --scheduler.type=cosine_decay_with_warmup \
    --scheduler.num_warmup_steps=5000 \
    --scheduler.num_decay_steps=250000 \
    --scheduler.peak_lr=3e-5 \
    --scheduler.decay_lr=1e-6 \
    --eval_freq=0

# ACT模型离线评估

# 基本评估（使用完整数据集）
conda activate lerobot && python script/act_eval_random.py \
    --model_path outputs/train/pick_up_parts_less_act_20250717_172222/checkpoints/last/pretrained_model \
    --dataset_repo_id pick_up_parts_less \
    --dataset_root /home/<USER>/data/testlerobot/lerobot \
    --n_samples 100 \
    --output_file scripts/act_eval_random_results.json

# 使用指定episodes评估
conda activate lerobot && python script/act_eval_random.py \
    --model_path outputs/train/pick_up_parts_less_act_chunk10_20250718_201017/checkpoints/last/pretrained_model \
    --dataset_repo_id pick_up_parts_less \
    --dataset_root /home/<USER>/data/testlerobot/lerobot \
    --episodes "[10,11,12,13,14]" \
    --n_samples 10 \
    --output_file scripts/act_eval_specified_episodes_results.json

# 单个episode可视化（默认每3步画一条线）
# ACT模型
conda activate lerobot && python script/act_eval_episode.py \
    --model_path outputs/train/act_pickup_chunk20_batch100_w5k_cos_20250808_222637/checkpoints/010000/pretrained_model \
    --dataset_repo_id pick_up \
    --dataset_root /home/<USER>/data/tmp/tiangong/lerobot \
    --episode_idx 100 \
    --plot_interval 3 \
    --save_debug_images 5 \
    --max_steps 1000
